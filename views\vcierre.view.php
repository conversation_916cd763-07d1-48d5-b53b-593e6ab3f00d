<?php
#region DOCS

/** @var array $citas_cierre Lista de citas incluidas en el cierre */
/** @var array $ventas_cierre Lista de ventas incluidas en el cierre */
/** @var array $ordenes_compra_cierre Lista de órdenes de compra incluidas en el cierre */
/** @var array $gastos_operativos_cierre Lista de gastos operativos incluidos en el cierre */
/** @var object $cierre Objeto Cierre con la información del cierre */
/** @var object $centro_costo Centro de costo del cierre */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Ver Cierre de Caja</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
        <!-- BEGIN container -->
        <div class="container">
            <!-- BEGIN row -->
            <div class="row justify-content-center">
                <!-- BEGIN col-12 -->
                <div class="col-12">

            <?php #region PAGE HEADER ?>
            <div class="d-flex align-items-center mb-3">
                <div>
                    <h4 class="mb-0">Cierre de Caja - Ver Detalles</h4>
                    <?php if ($cierre): ?>
                    <p class="mb-0 text-muted">
                        <strong>Fecha:</strong> <?php echo date('d/m/Y', strtotime($cierre->getFecha())); ?> | 
                        <strong>Centro de Costo:</strong> <?php echo htmlspecialchars($centro_costo->getNombre() ?? 'No disponible'); ?>
                    </p>
                    <?php endif; ?>
                </div>
                <div class="ms-auto">
                    <a href="cerrar-caja" class="btn btn-secondary">
                        <i class="fa fa-arrow-left fa-fw me-1"></i> Volver a Cierre de Caja
                    </a>
                </div>
            </div>

            <hr>
            <?php #endregion PAGE HEADER ?>

            <?php #region ERROR/SUCCESS MESSAGES ?>
            <?php if (isset($error_display) && $error_display === 'show'): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fa fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error_text); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($success_display) && $success_display === 'show'): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fa fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_text); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            <?php #endregion ERROR/SUCCESS MESSAGES ?>

            <?php if ($cierre && !isset($error_display)): ?>
            <!-- BEGIN summary-panel -->
            <div class="panel panel-inverse">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Resumen del Cierre
                    </h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold text-primary">Total Citas:</label>
                                <div class="form-control-plaintext text-white fs-5">
                                    <?php echo format_currency_consigno($cierre->getValorTotalCitas() ?? 0); ?>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold text-primary">Total Comisiones:</label>
                                <div class="form-control-plaintext text-white fs-5">
                                    <?php echo format_currency_consigno($cierre->getValorComisionTotalCitas() ?? 0); ?>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold text-primary">Total Ventas:</label>
                                <div class="form-control-plaintext text-white fs-5">
                                    <?php echo format_currency_consigno($cierre->getValorTotalVentas() ?? 0); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold text-primary">Total Órdenes de Compra:</label>
                                <div class="form-control-plaintext text-white fs-5">
                                    <?php echo format_currency_consigno($cierre->getValorTotalOrdenesCompra() ?? 0); ?>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold text-primary">Total Gastos Operativos:</label>
                                <div class="form-control-plaintext text-white fs-5">
                                    <?php echo format_currency_consigno($cierre->getValorTotalGastosOperativos() ?? 0); ?>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold text-success">Total General:</label>
                                <div class="form-control-plaintext text-success fs-4 fw-bold">
                                    <?php 
                                    $total_general = ($cierre->getValorTotalCitas() ?? 0) + 
                                                   ($cierre->getValorTotalVentas() ?? 0) - 
                                                   ($cierre->getValorTotalOrdenesCompra() ?? 0) - 
                                                   ($cierre->getValorTotalGastosOperativos() ?? 0);
                                    echo format_currency_consigno($total_general); 
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END summary-panel -->

            <!-- BEGIN citas-panel -->
            <div class="panel panel-inverse mt-4">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Citas Incluidas en el Cierre
                        <span class="badge bg-primary ms-2"><?php echo count($citas_cierre ?? []); ?></span>
                    </h4>
                </div>
                <div class="panel-body p-0">
                    <?php if (empty($citas_cierre ?? [])): ?>
                        <!-- No results message -->
                        <div class="text-center p-4">
                            <i class="fa fa-info-circle fa-3x text-info mb-3"></i>
                            <h5 class="text-muted">No hay citas incluidas en este cierre</h5>
                        </div>
                    <?php else: ?>
                        <!-- Results table -->
                        <div style="overflow-x: auto;">
                            <table class="table table-hover table-sm mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 60px;">#</th>
                                        <th style="width: 140px;">Fecha Inicio</th>
                                        <th style="width: 140px;">Fecha Fin</th>
                                        <th>Empleado</th>
                                        <th>Puesto</th>
                                        <th>Método de Pago</th>
                                        <th class="text-end" style="width: 120px;">Total Servicios</th>
                                        <th class="text-end" style="width: 120px;">Comisión</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $total_servicios_citas = 0;
                                    $total_comisiones_citas = 0;
                                    foreach ($citas_cierre as $cita):
                                        $total_servicios = $cita->getTotal_valor_servicios() ?? 0;
                                        $comision = $cita->getValor_comision_empleado() ?? 0;
                                        $total_servicios_citas += $total_servicios;
                                        $total_comisiones_citas += $comision;
                                    ?>
                                        <tr>
                                            <td><?php echo $cita->getId(); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($cita->getFecha_inicio())); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($cita->getFecha_fin())); ?></td>
                                            <td><?php echo htmlspecialchars($cita->getNombre_empleado() ?? 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($cita->getDescripcion_puesto() ?? 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($cita->getNombre_metodo_pago() ?? 'N/A'); ?></td>
                                            <td class="text-end"><?php echo format_currency_consigno($total_servicios); ?></td>
                                            <td class="text-end"><?php echo format_currency_consigno($comision); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <th colspan="6" class="text-end fw-bold">Totales:</th>
                                        <th class="text-end fw-bold"><?php echo format_currency_consigno($total_servicios_citas); ?></th>
                                        <th class="text-end fw-bold"><?php echo format_currency_consigno($total_comisiones_citas); ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- END citas-panel -->

            <!-- BEGIN ventas-panel -->
            <div class="panel panel-inverse mt-4">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Ventas Incluidas en el Cierre
                        <span class="badge bg-primary ms-2"><?php echo count($ventas_cierre ?? []); ?></span>
                    </h4>
                </div>
                <div class="panel-body p-0">
                    <?php if (empty($ventas_cierre ?? [])): ?>
                        <!-- No results message -->
                        <div class="text-center p-4">
                            <i class="fa fa-info-circle fa-3x text-info mb-3"></i>
                            <h5 class="text-muted">No hay ventas incluidas en este cierre</h5>
                        </div>
                    <?php else: ?>
                        <!-- Results table -->
                        <div style="overflow-x: auto;">
                            <table class="table table-hover table-sm mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 60px;">#</th>
                                        <th style="width: 140px;">Fecha</th>
                                        <th>Cliente</th>
                                        <th>Método de Pago</th>
                                        <th class="text-end" style="width: 120px;">Valor Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $total_ventas_cierre = 0;
                                    foreach (($ventas_cierre ?? []) as $venta):
                                        $valor_total = $venta->getValor_total() ?? 0;
                                        $total_ventas_cierre += $valor_total;
                                    ?>
                                        <tr>
                                            <td><?php echo $venta->getId(); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($venta->getFecha())); ?></td>
                                            <td><?php echo htmlspecialchars($venta->getCliente_nombre() ?? 'Sin cliente'); ?></td>
                                            <td><?php echo htmlspecialchars($venta->getMetodo_pago_nombre() ?? 'N/A'); ?></td>
                                            <td class="text-end"><?php echo format_currency_consigno($valor_total); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th colspan="4" class="text-end fw-bold">Total Ventas:</th>
                                        <th class="text-end fw-bold"><?php echo format_currency_consigno($total_ventas_cierre); ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- END ventas-panel -->

            <!-- BEGIN ordenes-compra-panel -->
            <div class="panel panel-inverse mt-4">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Órdenes de Compra Incluidas en el Cierre
                        <span class="badge bg-primary ms-2"><?php echo count($ordenes_compra_cierre ?? []); ?></span>
                    </h4>
                </div>
                <div class="panel-body p-0">
                    <?php if (empty($ordenes_compra_cierre ?? [])): ?>
                        <!-- No results message -->
                        <div class="text-center p-4">
                            <i class="fa fa-info-circle fa-3x text-info mb-3"></i>
                            <h5 class="text-muted">No hay órdenes de compra incluidas en este cierre</h5>
                        </div>
                    <?php else: ?>
                        <!-- Results table -->
                        <div style="overflow-x: auto;">
                            <table class="table table-hover table-sm mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 60px;">#</th>
                                        <th style="width: 140px;">Fecha</th>
                                        <th>Proveedor</th>
                                        <th style="width: 120px;">Núm. Referencia</th>
                                        <th class="text-end" style="width: 120px;">Valor Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $total_ordenes_cierre = 0;
                                    foreach (($ordenes_compra_cierre ?? []) as $orden):
                                        $valor_total = $orden->getValor_total() ?? 0;
                                        $total_ordenes_cierre += $valor_total;
                                    ?>
                                        <tr>
                                            <td><?php echo $orden->getId(); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($orden->getFecha())); ?></td>
                                            <td><?php echo htmlspecialchars($orden->getProveedor_nombre() ?? 'Sin proveedor'); ?></td>
                                            <td><?php echo htmlspecialchars($orden->getN_referencia_proveedor() ?? ''); ?></td>
                                            <td class="text-end"><?php echo format_currency_consigno($valor_total); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th colspan="4" class="text-end fw-bold">Total Órdenes de Compra:</th>
                                        <th class="text-end fw-bold"><?php echo format_currency_consigno($total_ordenes_cierre); ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- END ordenes-compra-panel -->

            <!-- BEGIN gastos-operativos-panel -->
            <div class="panel panel-inverse mt-4">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Gastos Operativos Incluidos en el Cierre
                        <span class="badge bg-primary ms-2"><?php echo count($gastos_operativos_cierre ?? []); ?></span>
                    </h4>
                </div>
                <div class="panel-body p-0">
                    <?php if (empty($gastos_operativos_cierre ?? [])): ?>
                        <!-- No results message -->
                        <div class="text-center p-4">
                            <i class="fa fa-info-circle fa-3x text-info mb-3"></i>
                            <h5 class="text-muted">No hay gastos operativos incluidos en este cierre</h5>
                        </div>
                    <?php else: ?>
                        <!-- Results table -->
                        <div style="overflow-x: auto;">
                            <table class="table table-hover table-sm mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 60px;">#</th>
                                        <th style="width: 140px;">Fecha</th>
                                        <th>Descripción</th>
                                        <th class="text-end" style="width: 120px;">Valor</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $total_gastos_cierre = 0;
                                    foreach (($gastos_operativos_cierre ?? []) as $gasto):
                                        $valor = $gasto->getValor() ?? 0;
                                        $total_gastos_cierre += $valor;
                                    ?>
                                        <tr>
                                            <td><?php echo $gasto->getId(); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($gasto->getFecha())); ?></td>
                                            <td><?php echo htmlspecialchars($gasto->getDescripcion() ?? 'N/A'); ?></td>
                                            <td class="text-end"><?php echo format_currency_consigno($valor); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th colspan="3" class="text-end fw-bold">Total Gastos Operativos:</th>
                                        <th class="text-end fw-bold"><?php echo format_currency_consigno($total_gastos_cierre); ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- END gastos-operativos-panel -->
            <?php endif; ?>

        </div>
        <!-- END col-12 -->
    </div>
    <!-- END row -->
</div>
<!-- END container -->
</div>
<!-- END #content -->
</div>
<!-- END #app -->

<!-- ================== BEGIN core-js ================== -->
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>
<!-- ================== END core-js ================== -->

</body>
</html>
