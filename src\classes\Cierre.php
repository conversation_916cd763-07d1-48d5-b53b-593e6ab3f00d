<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Cierre
{
	// --- Atributos ---
	private ?int    $id                           = null;
	private ?string $fecha                        = null;
	private ?int    $estado                       = null;
	private ?float  $valor_total_citas            = null;
	private ?float  $valor_comision_total_citas   = null;
	private ?float  $valor_total_ventas           = null;
	private ?float  $valor_total_ordenes_compra   = null;
	private ?float  $valor_total_gastos_operativos = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Cierre.
	 */
	public function __construct()
	{
		$this->id                           = 0; // O null si prefieres no usar 0 por defecto
		$this->fecha                        = null;
		$this->estado                       = 1; // Estado activo por defecto
		$this->valor_total_citas            = 0.0;
		$this->valor_comision_total_citas   = 0.0;
		$this->valor_total_ventas           = 0.0;
		$this->valor_total_ordenes_compra   = 0.0;
		$this->valor_total_gastos_operativos = 0.0;
	}

	/**
	 * Método estático para construir un objeto Cierre desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del cierre.
	 *
	 * @return self Instancia de Cierre.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                                   = new self();
			$objeto->id                               = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->fecha                            = $resultado['fecha'] ?? null;
			$objeto->estado                           = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			$objeto->valor_total_citas                = isset($resultado['valor_total_citas']) ? (float)$resultado['valor_total_citas'] : 0.0;
			$objeto->valor_comision_total_citas       = isset($resultado['valor_comision_total_citas']) ? (float)$resultado['valor_comision_total_citas'] : 0.0;
			$objeto->valor_total_ventas               = isset($resultado['valor_total_ventas']) ? (float)$resultado['valor_total_ventas'] : 0.0;
			$objeto->valor_total_ordenes_compra       = isset($resultado['valor_total_ordenes_compra']) ? (float)$resultado['valor_total_ordenes_compra'] : 0.0;
			$objeto->valor_total_gastos_operativos    = isset($resultado['valor_total_gastos_operativos']) ? (float)$resultado['valor_total_gastos_operativos'] : 0.0;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Cierre: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un cierre por su ID.
	 *
	 * @param int $id       ID del cierre.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Cierre o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener cierre por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM cierres
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Cierre (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de cierres activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Cierre.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de cierres activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM cierres
            WHERE
            	estado = 1
            ORDER BY
            	fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Cierres: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene todos los cierres (incluyendo inactivos).
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Cierre.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerTodos(PDO $conexion): array
	{
		try {
			// Consulta para obtener todos los cierres (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM cierres
            ORDER BY
            	fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener todos los Cierres: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un cierre por su fecha.
	 *
	 * @param string $fecha    Fecha del cierre (formato YYYY-MM-DD).
	 * @param PDO    $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Cierre o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorFecha(string $fecha, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener cierre por fecha (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM cierres
            WHERE
            	fecha = :fecha
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":fecha", $fecha, PDO::PARAM_STR);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Cierre por fecha ($fecha): " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo cierre en la base de datos a partir de un objeto Cierre.
	 * El objeto Cierre debe estar completamente poblado.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo cierre creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getFecha())) {
			throw new Exception("Fecha es requerida en el objeto Cierre para crearlo.");
		}

		// Validar formato de fecha
		if (!$this->validarFecha($this->getFecha())) {
			throw new Exception("La fecha debe tener un formato válido (YYYY-MM-DD).");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO cierres (
            	 fecha
            	,estado
            	,valor_total_citas
            	,valor_comision_total_citas
            	,valor_total_ventas
            	,valor_total_ordenes_compra
            	,valor_total_gastos_operativos
            ) VALUES (
            	 :fecha
            	,:estado
            	,:valor_total_citas
            	,:valor_comision_total_citas
            	,:valor_total_ventas
            	,:valor_total_ordenes_compra
            	,:valor_total_gastos_operativos
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':valor_total_citas', $this->getValorTotalCitas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_comision_total_citas', $this->getValorComisionTotalCitas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_ventas', $this->getValorTotalVentas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_ordenes_compra', $this->getValorTotalOrdenesCompra(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_gastos_operativos', $this->getValorTotalGastosOperativos(), PDO::PARAM_STR);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del cierre recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. fecha duplicada)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al crear cierre: Ya existe un cierre para la fecha '{$this->getFecha()}'.");
			} else {
				throw new Exception("Error de base de datos al crear cierre: " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear cierre: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza un cierre existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validar que el ID exista
		if (empty($this->getId())) {
			throw new Exception("ID es requerido para actualizar un Cierre.");
		}

		// Validaciones básicas sobre el objeto
		if (empty($this->getFecha())) {
			throw new Exception("Fecha es requerida en el objeto Cierre para modificarlo.");
		}

		// Validar formato de fecha
		if (!$this->validarFecha($this->getFecha())) {
			throw new Exception("La fecha debe tener un formato válido (YYYY-MM-DD).");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Consulta para actualizar el cierre
			$query = <<<SQL
            UPDATE cierres SET
                fecha = :fecha,
                estado = :estado,
                valor_total_citas = :valor_total_citas,
                valor_comision_total_citas = :valor_comision_total_citas,
                valor_total_ventas = :valor_total_ventas,
                valor_total_ordenes_compra = :valor_total_ordenes_compra,
                valor_total_gastos_operativos = :valor_total_gastos_operativos
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':valor_total_citas', $this->getValorTotalCitas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_comision_total_citas', $this->getValorComisionTotalCitas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_ventas', $this->getValorTotalVentas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_ordenes_compra', $this->getValorTotalOrdenesCompra(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_gastos_operativos', $this->getValorTotalGastosOperativos(), PDO::PARAM_STR);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. fecha duplicada)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al modificar cierre: Ya existe un cierre para la fecha '{$this->getFecha()}'.");
			} else {
				throw new Exception("Error de base de datos al modificar cierre (ID: {$this->getId()}): " . $e->getMessage());
			}
		}
	}

	/**
	 * Método privado para insertar un nuevo cierre en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo cierre creado o false en caso de error.
	 * @throws Exception Si hay error en DB.
	 */
	private function _insert(PDO $conexion): int|false
	{
		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO cierres (
            	 fecha
            	,estado
            	,valor_total_citas
            	,valor_comision_total_citas
            	,valor_total_ventas
            	,valor_total_ordenes_compra
            	,valor_total_gastos_operativos
            ) VALUES (
            	 :fecha
            	,:estado
            	,:valor_total_citas
            	,:valor_comision_total_citas
            	,:valor_total_ventas
            	,:valor_total_ordenes_compra
            	,:valor_total_gastos_operativos
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':valor_total_citas', $this->getValorTotalCitas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_comision_total_citas', $this->getValorComisionTotalCitas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_ventas', $this->getValorTotalVentas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_ordenes_compra', $this->getValorTotalOrdenesCompra(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_gastos_operativos', $this->getValorTotalGastosOperativos(), PDO::PARAM_STR);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del cierre recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al insertar cierre: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al insertar cierre: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para actualizar un cierre existente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private function _update(PDO $conexion): bool
	{
		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Consulta para actualizar el cierre
			$query = <<<SQL
            UPDATE cierres SET
                fecha = :fecha,
                estado = :estado,
                valor_total_citas = :valor_total_citas,
                valor_comision_total_citas = :valor_comision_total_citas,
                valor_total_ventas = :valor_total_ventas,
                valor_total_ordenes_compra = :valor_total_ordenes_compra,
                valor_total_gastos_operativos = :valor_total_gastos_operativos
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':valor_total_citas', $this->getValorTotalCitas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_comision_total_citas', $this->getValorComisionTotalCitas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_ventas', $this->getValorTotalVentas(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_ordenes_compra', $this->getValorTotalOrdenesCompra(), PDO::PARAM_STR);
			$statement->bindValue(':valor_total_gastos_operativos', $this->getValorTotalGastosOperativos(), PDO::PARAM_STR);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al actualizar cierre (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Elimina un cierre por su ID (eliminación lógica - cambia estado a 0).
	 *
	 * @param int $id       ID del cierre a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar lógicamente el cierre (cambiar estado a 0)
			$query = <<<SQL
            UPDATE cierres SET
                estado = 0
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al eliminar cierre (ID: $id): " . $e->getMessage());
		}
	}

	// --- Métodos de Validación ---

	/**
	 * Valida que una fecha tenga el formato correcto (YYYY-MM-DD).
	 *
	 * @param string|null $fecha Fecha a validar.
	 *
	 * @return bool True si la fecha es válida, False en caso contrario.
	 */
	private function validarFecha(?string $fecha): bool
	{
		if (empty($fecha)) {
			return false;
		}

		// Validar formato YYYY-MM-DD usando DateTime
		$dateTime = \DateTime::createFromFormat('Y-m-d', $fecha);
		return $dateTime && $dateTime->format('Y-m-d') === $fecha;
	}

	/**
	 * Valida que el estado sea un valor válido (0 o 1).
	 *
	 * @param int|null $estado Estado a validar.
	 *
	 * @return bool True si el estado es válido, False en caso contrario.
	 */
	private function validarEstado(?int $estado): bool
	{
		return $estado === 0 || $estado === 1;
	}

	/**
	 * Valida que un valor monetario sea válido (no negativo).
	 *
	 * @param float|null $valor Valor a validar.
	 *
	 * @return bool True si el valor es válido, False en caso contrario.
	 */
	private function validarValorMonetario(?float $valor): bool
	{
		return $valor === null || $valor >= 0.0;
	}

	// --- Getters y Setters ---

	/**
	 * Obtiene el ID del cierre.
	 *
	 * @return int|null ID del cierre.
	 */
	public function getId(): ?int
	{
		return $this->id;
	}

	/**
	 * Establece el ID del cierre.
	 *
	 * @param int|null $id ID del cierre.
	 *
	 * @return self
	 */
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	/**
	 * Obtiene la fecha del cierre.
	 *
	 * @return string|null Fecha del cierre.
	 */
	public function getFecha(): ?string
	{
		return $this->fecha;
	}

	/**
	 * Establece la fecha del cierre.
	 *
	 * @param string|null $fecha Fecha del cierre (formato YYYY-MM-DD).
	 *
	 * @return self
	 * @throws Exception Si la fecha no tiene un formato válido.
	 */
	public function setFecha(?string $fecha): self
	{
		if ($fecha !== null && !$this->validarFecha($fecha)) {
			throw new Exception("La fecha debe tener un formato válido (YYYY-MM-DD).");
		}
		$this->fecha = $fecha;
		return $this;
	}

	/**
	 * Obtiene el estado del cierre.
	 *
	 * @return int|null Estado del cierre (1 = activo, 0 = inactivo).
	 */
	public function getEstado(): ?int
	{
		return $this->estado;
	}

	/**
	 * Establece el estado del cierre.
	 *
	 * @param int|null $estado Estado del cierre (1 = activo, 0 = inactivo).
	 *
	 * @return self
	 * @throws Exception Si el estado no es válido.
	 */
	public function setEstado(?int $estado): self
	{
		if ($estado !== null && !$this->validarEstado($estado)) {
			throw new Exception("El estado debe ser 0 (inactivo) o 1 (activo).");
		}
		$this->estado = $estado;
		return $this;
	}

	/**
	 * Obtiene el valor total de citas.
	 *
	 * @return float|null Valor total de citas.
	 */
	public function getValorTotalCitas(): ?float
	{
		return $this->valor_total_citas;
	}

	/**
	 * Establece el valor total de citas.
	 *
	 * @param float|null $valor_total_citas Valor total de citas.
	 *
	 * @return self
	 * @throws Exception Si el valor no es válido.
	 */
	public function setValorTotalCitas(?float $valor_total_citas): self
	{
		if (!$this->validarValorMonetario($valor_total_citas)) {
			throw new Exception("El valor total de citas debe ser un número no negativo.");
		}
		$this->valor_total_citas = $valor_total_citas;
		return $this;
	}

	/**
	 * Obtiene el valor total de comisiones de citas.
	 *
	 * @return float|null Valor total de comisiones de citas.
	 */
	public function getValorComisionTotalCitas(): ?float
	{
		return $this->valor_comision_total_citas;
	}

	/**
	 * Establece el valor total de comisiones de citas.
	 *
	 * @param float|null $valor_comision_total_citas Valor total de comisiones de citas.
	 *
	 * @return self
	 * @throws Exception Si el valor no es válido.
	 */
	public function setValorComisionTotalCitas(?float $valor_comision_total_citas): self
	{
		if (!$this->validarValorMonetario($valor_comision_total_citas)) {
			throw new Exception("El valor total de comisiones de citas debe ser un número no negativo.");
		}
		$this->valor_comision_total_citas = $valor_comision_total_citas;
		return $this;
	}

	/**
	 * Obtiene el valor total de ventas.
	 *
	 * @return float|null Valor total de ventas.
	 */
	public function getValorTotalVentas(): ?float
	{
		return $this->valor_total_ventas;
	}

	/**
	 * Establece el valor total de ventas.
	 *
	 * @param float|null $valor_total_ventas Valor total de ventas.
	 *
	 * @return self
	 * @throws Exception Si el valor no es válido.
	 */
	public function setValorTotalVentas(?float $valor_total_ventas): self
	{
		if (!$this->validarValorMonetario($valor_total_ventas)) {
			throw new Exception("El valor total de ventas debe ser un número no negativo.");
		}
		$this->valor_total_ventas = $valor_total_ventas;
		return $this;
	}

	/**
	 * Obtiene el valor total de órdenes de compra.
	 *
	 * @return float|null Valor total de órdenes de compra.
	 */
	public function getValorTotalOrdenesCompra(): ?float
	{
		return $this->valor_total_ordenes_compra;
	}

	/**
	 * Establece el valor total de órdenes de compra.
	 *
	 * @param float|null $valor_total_ordenes_compra Valor total de órdenes de compra.
	 *
	 * @return self
	 * @throws Exception Si el valor no es válido.
	 */
	public function setValorTotalOrdenesCompra(?float $valor_total_ordenes_compra): self
	{
		if (!$this->validarValorMonetario($valor_total_ordenes_compra)) {
			throw new Exception("El valor total de órdenes de compra debe ser un número no negativo.");
		}
		$this->valor_total_ordenes_compra = $valor_total_ordenes_compra;
		return $this;
	}

	/**
	 * Obtiene el valor total de gastos operativos.
	 *
	 * @return float|null Valor total de gastos operativos.
	 */
	public function getValorTotalGastosOperativos(): ?float
	{
		return $this->valor_total_gastos_operativos;
	}

	/**
	 * Establece el valor total de gastos operativos.
	 *
	 * @param float|null $valor_total_gastos_operativos Valor total de gastos operativos.
	 *
	 * @return self
	 * @throws Exception Si el valor no es válido.
	 */
	public function setValorTotalGastosOperativos(?float $valor_total_gastos_operativos): self
	{
		if (!$this->validarValorMonetario($valor_total_gastos_operativos)) {
			throw new Exception("El valor total de gastos operativos debe ser un número no negativo.");
		}
		$this->valor_total_gastos_operativos = $valor_total_gastos_operativos;
		return $this;
	}
}
