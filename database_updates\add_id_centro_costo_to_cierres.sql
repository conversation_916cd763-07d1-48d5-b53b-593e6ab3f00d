-- Add id_centro_costo field to cierres table
-- This field is required for the cash register closing functionality

ALTER TABLE cierres 
ADD COLUMN id_centro_costo INT NULL AFTER fecha,
ADD CONSTRAINT fk_cierres_centro_costo 
    FOREIGN KEY (id_centro_costo) 
    REFERENCES centros_costos(id) 
    ON DELETE RESTRICT 
    ON UPDATE CASCADE;

-- Add index for better performance
CREATE INDEX idx_cierres_centro_costo ON cierres(id_centro_costo);

-- Add index for cierre lookups
CREATE INDEX idx_citas_cierre ON citas(id_cierre);
CREATE INDEX idx_ventas_cierre ON ventas(id_cierre);
CREATE INDEX idx_ordenes_compra_cierre ON ordenes_compra(id_cierre);
CREATE INDEX idx_gastos_operativos_cierre ON gastos_operativos(id_cierre);
