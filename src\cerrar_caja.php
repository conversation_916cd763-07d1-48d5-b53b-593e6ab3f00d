<?php
/**
 * Controlador para el cierre de caja
 * 
 * Este controlador maneja la visualización de citas finalizadas listas para cierre de caja,
 * incluyendo funcionalidades de consulta de detalles de citas.
 */

use App\classes\Cita;
use App\classes\Venta;
use App\classes\VentaDetalle;
use App\classes\GastoOperativo;
use App\classes\OrdenCompra;
use App\classes\OrdenCompraDetalle;
use App\classes\CentroCosto;
use App\classes\Cierre;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en cerrar_caja.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Initialize response for AJAX requests
$response = ['success' => false, 'message' => ''];

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    #region Get Cita Details
    if ($action === 'get_cita_details') {
        $citaId = !empty($_POST['cita_id']) ? (int)$_POST['cita_id'] : 0;

        if ($citaId > 0) {
            try {
                // Get the cita header information
                $cita = Cita::get($citaId, $conexion);

                if (!$cita) {
                    $response['message'] = 'Cita no encontrada.';
                    http_response_code(404);
                } else {
                    // Get the cita services
                    $servicios = $cita->getServicios($conexion);

                    // Format cita header data
                    $cita_data = [
                        'id'                        => $cita->getId(),
                        'fecha_inicio'              => $cita->getFecha_inicio(),
                        'fecha_fin'                 => $cita->getFecha_fin(),
                        'fecha_inicio_formateada'   => date('Y-m-d H:i:s', strtotime($cita->getFecha_inicio())),
                        'fecha_fin_formateada'      => date('Y-m-d H:i:s', strtotime($cita->getFecha_fin())),
                        'empleado_nombre'           => $cita->getNombre_empleado(),
                        'puesto_descripcion'        => $cita->getDescripcion_puesto(),
                        'metodo_pago_nombre'        => $cita->getNombre_metodo_pago(),
                        'valor_comision_empleado'   => $cita->getValor_comision_empleado(),
                        'valor_comision_formateado' => format_currency_consigno($cita->getValor_comision_empleado()),
                        'estado'                    => $cita->getEstado(),
                        'estado_texto'              => $cita->getEstado() === 1 ? 'Terminada' : 'Cancelada'
                    ];

                    // Format services data
                    $servicios_data = [];
                    $total_servicios = 0;
                    foreach ($servicios as $servicio) {
                        $valor_servicio = $servicio->getValor();
                        $servicios_data[] = [
                            'id'               => $servicio->getId(),
                            'descripcion'      => $servicio->getDescripcion(),
                            'valor'            => $valor_servicio,
                            'valor_formateado' => format_currency_consigno($valor_servicio)
                        ];
                        $total_servicios += $valor_servicio;
                    }

                    $response['success']                    = true;
                    $response['cita']                       = $cita_data;
                    $response['servicios']                  = $servicios_data;
                    $response['total_servicios']            = $total_servicios;
                    $response['total_servicios_formateado'] = format_currency_consigno($total_servicios);
                }

            } catch (Exception $e) {
                $response['message'] = "Error al obtener detalles de cita: " . $e->getMessage();
                http_response_code(500);
            }
        } else {
            $response['message'] = 'ID de cita inválido.';
            http_response_code(400);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Get Cita Details

    #region Get Venta Details
    if ($action === 'get_venta_details') {
        $ventaId = !empty($_POST['venta_id']) ? (int)$_POST['venta_id'] : 0;

        if ($ventaId > 0) {
            try {
                // Get the venta header information
                $venta = Venta::get($ventaId, $conexion);

                if (!$venta) {
                    $response['message'] = 'Venta no encontrada.';
                    http_response_code(404);
                } else {
                    // Get the venta details
                    $venta_detalles = VentaDetalle::get_by_venta($ventaId, $conexion);

                    // Format venta header data
                    $venta_data = [
                        'id'                     => $venta->getId(),
                        'fecha'                  => $venta->getFecha(),
                        'fecha_formateada'       => date('Y-m-d H:i:s', strtotime($venta->getFecha())),
                        'cliente_nombre'         => $venta->getCliente_nombre() ?? 'Sin cliente',
                        'centro_costo_nombre'    => $venta->getCentro_costo_nombre(),
                        'metodo_pago_nombre'     => $venta->getMetodo_pago_nombre(),
                        'valor_total'            => $venta->getValor_total(),
                        'valor_total_formateado' => format_currency_consigno($venta->getValor_total()),
                        'estado'                 => $venta->getEstado(),
                        'estado_texto'           => $venta->getEstado() === 1 ? 'Activa' : 'Cancelada'
                    ];

                    // Format venta details data
                    $detalles_data = [];
                    $total_detalles = 0;
                    foreach ($venta_detalles as $detalle) {
                        $valor_total_detalle = $detalle->getValor_total();
                        $detalles_data[] = [
                            'id'                     => $detalle->getId(),
                            'producto_descripcion'   => $detalle->getProducto_descripcion(),
                            'cantidad'               => $detalle->getCantidad(),
                            'valor'                  => $detalle->getValor(),
                            'valor_formateado'       => format_currency_consigno($detalle->getValor()),
                            'valor_total'            => $valor_total_detalle,
                            'valor_total_formateado' => format_currency_consigno($valor_total_detalle)
                        ];
                        $total_detalles += $valor_total_detalle;
                    }

                    $response['success']                   = true;
                    $response['venta']                     = $venta_data;
                    $response['detalles']                  = $detalles_data;
                    $response['total_detalles']            = $total_detalles;
                    $response['total_detalles_formateado'] = format_currency_consigno($total_detalles);
                }

            } catch (Exception $e) {
                $response['message'] = "Error al obtener detalles de venta: " . $e->getMessage();
                http_response_code(500);
            }
        } else {
            $response['message'] = 'ID de venta inválido.';
            http_response_code(400);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Get Venta Details

    #region Get Orden Compra Details
    if ($action === 'get_orden_compra_details') {
        $ordenId = !empty($_POST['orden_id']) ? (int)$_POST['orden_id'] : 0;

        if ($ordenId > 0) {
            try {
                // Get the orden compra header information
                $orden = OrdenCompra::obtener($ordenId, $conexion);

                if (!$orden) {
                    $response['message'] = 'Orden de compra no encontrada.';
                    http_response_code(404);
                } else {
                    // Get the orden compra details
                    $detalles = OrdenCompraDetalle::obtenerPorOrdenCompra($ordenId, $conexion);

                    // Format orden data
                    $orden_data = [
                        'id'                     => $orden->getId(),
                        'fecha_formateada'       => date('Y-m-d', strtotime($orden->getFecha())),
                        'proveedor_nombre'       => $orden->getProveedor_nombre(),
                        'centro_costo_nombre'    => $orden->getCentro_costo_nombre(),
                        'usuario_nombre'         => $orden->getUsuario_nombre(),
                        'n_referencia_proveedor' => $orden->getN_referencia_proveedor(),
                        'valor_total_formateado' => format_currency_consigno($orden->getValor_total())
                    ];

                    // Format details data
                    $detalles_data = [];
                    $total_detalles = 0;
                    foreach ($detalles as $detalle) {
                        $valor_total_detalle = $detalle->getValor_total();
                        $total_detalles += $valor_total_detalle;

                        $detalles_data[] = [
                            'producto_descripcion'   => $detalle->getProducto_descripcion(),
                            'cantidad'               => $detalle->getCantidad(),
                            'valor_formateado'       => format_currency_consigno($detalle->getValor()),
                            'valor_total_formateado' => format_currency_consigno($valor_total_detalle)
                        ];
                    }

                    $response['success']                   = true;
                    $response['orden']                     = $orden_data;
                    $response['detalles']                  = $detalles_data;
                    $response['total_detalles_formateado'] = format_currency_consigno($total_detalles);
                }

            } catch (Exception $e) {
                $response['message'] = "Error al obtener detalles de la orden de compra: " . $e->getMessage();
                http_response_code(500);
            }
        } else {
            $response['message'] = 'ID de orden de compra inválido.';
            http_response_code(400);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Get Orden Compra Details

    #region Generar Cierre
    if ($action === 'generar_cierre') {
        // Log the request for debugging
        error_log("Generar cierre request received");

        try {
            // Validate session and centro de costo
            if (!isset($_SESSION[CENTRO_COSTO_SESSION]) || empty($_SESSION[CENTRO_COSTO_SESSION])) {
                error_log("Error: No centro de costo in session");
                throw new Exception('No hay un centro de costo seleccionado en la sesión.');
            }

            $id_centro_costo = (int)$_SESSION[CENTRO_COSTO_SESSION];
            error_log("Centro de costo ID: " . $id_centro_costo);

            // Get all data for cierre
            $citas_para_cierre = Cita::getCitasParaCierreByCentroCosto($id_centro_costo, $conexion);
            $ventas_para_cierre = Venta::getVentasParaCierreByCentroCosto($id_centro_costo, $conexion);
            $ordenes_compra_para_cierre = OrdenCompra::getOrdenesCompraParaCierreByCentroCosto($id_centro_costo, $conexion);
            $gastos_operativos_para_cierre = GastoOperativo::getGastosOperativosParaCierre($conexion);

            // Calculate totals
            $total_citas = 0;
            $total_comisiones = 0;
            foreach ($citas_para_cierre as $cita) {
                $total_citas += $cita->getTotal_valor_servicios() ?? 0;
                $total_comisiones += $cita->getValor_comision_empleado() ?? 0;
            }

            $total_ventas = 0;
            foreach ($ventas_para_cierre as $venta) {
                $total_ventas += $venta->getValor_total() ?? 0;
            }

            $total_ordenes_compra = 0;
            foreach ($ordenes_compra_para_cierre as $orden) {
                $total_ordenes_compra += $orden->getValor_total() ?? 0;
            }

            $total_gastos_operativos = 0;
            foreach ($gastos_operativos_para_cierre as $gasto) {
                $total_gastos_operativos += $gasto->getValor() ?? 0;
            }

            // Create Cierre object
            $cierre = new Cierre();
            $cierre->setFecha(date('Y-m-d'));
            $cierre->setId_centro_costo($id_centro_costo);
            $cierre->setEstado(1);
            $cierre->setValorTotalCitas($total_citas);
            $cierre->setValorComisionTotalCitas($total_comisiones);
            $cierre->setValorTotalVentas($total_ventas);
            $cierre->setValorTotalOrdenesCompra($total_ordenes_compra);
            $cierre->setValorTotalGastosOperativos($total_gastos_operativos);

            // Start transaction
            error_log("Starting database transaction for cierre creation");
            $conexion->beginTransaction();

            try {
                // Create cierre record
                error_log("Creating cierre record");
                $id_cierre = $cierre->crear($conexion);
                if (!$id_cierre) {
                    error_log("Failed to create cierre record");
                    throw new Exception('Error al crear el registro de cierre.');
                }
                error_log("Cierre created successfully with ID: " . $id_cierre);

                // Update related records with cierre ID
                if (!empty($citas_para_cierre)) {
                    $ids_citas = array_map(fn($cita) => $cita->getId(), $citas_para_cierre);
                    error_log("Updating " . count($ids_citas) . " citas with cierre ID");
                    Cita::actualizarIdCierreBulk($ids_citas, $id_cierre, $conexion);
                }

                if (!empty($ventas_para_cierre)) {
                    $ids_ventas = array_map(fn($venta) => $venta->getId(), $ventas_para_cierre);
                    error_log("Updating " . count($ids_ventas) . " ventas with cierre ID");
                    Venta::actualizarIdCierreBulk($ids_ventas, $id_cierre, $conexion);
                }

                if (!empty($ordenes_compra_para_cierre)) {
                    $ids_ordenes = array_map(fn($orden) => $orden->getId(), $ordenes_compra_para_cierre);
                    error_log("Updating " . count($ids_ordenes) . " ordenes de compra with cierre ID");
                    OrdenCompra::actualizarIdCierreBulk($ids_ordenes, $id_cierre, $conexion);
                }

                if (!empty($gastos_operativos_para_cierre)) {
                    $ids_gastos = array_map(fn($gasto) => $gasto->getId(), $gastos_operativos_para_cierre);
                    error_log("Updating " . count($ids_gastos) . " gastos operativos with cierre ID");
                    GastoOperativo::actualizarIdCierreBulk($ids_gastos, $id_cierre, $conexion);
                }

                // Commit transaction
                error_log("Committing transaction");
                $conexion->commit();

                error_log("Cierre generation completed successfully");
                $response['success'] = true;
                $response['message'] = 'Cierre generado exitosamente';
                $response['id_cierre'] = $id_cierre;

            } catch (Exception $e) {
                error_log("Error in transaction, rolling back: " . $e->getMessage());
                $conexion->rollBack();
                throw $e;
            }

        } catch (Exception $e) {
            error_log("Error generating cierre: " . $e->getMessage());
            $response['message'] = "Error al generar cierre: " . $e->getMessage();
            http_response_code(500);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Generar Cierre
}
#endregion Handle POST Actions

#region try
try {
    // Set timezone for any date operations
    date_default_timezone_set('America/Bogota');

    // Verify that there is a centro de costo selected in session
    if (!isset($_SESSION[CENTRO_COSTO_SESSION]) || empty($_SESSION[CENTRO_COSTO_SESSION])) {
        throw new Exception('Error: No hay un centro de costo seleccionado. Por favor, seleccione un centro de costo antes de proceder.');
    }

    $id_centro_costo = (int)$_SESSION[CENTRO_COSTO_SESSION];

    // Get centro de costo information
    $centro_costo = CentroCosto::get($id_centro_costo, $conexion);
    if (!$centro_costo) {
        throw new Exception('Error: El centro de costo seleccionado no es válido.');
    }

    // Get appointments ready for cash closing
    $citas_para_cierre = Cita::getCitasParaCierreByCentroCosto($id_centro_costo, $conexion);

    // Get sales ready for cash closing
    $ventas_para_cierre = Venta::getVentasParaCierreByCentroCosto($id_centro_costo, $conexion);

    // Get purchase orders ready for cash closing
    $ordenes_compra_para_cierre = OrdenCompra::getOrdenesCompraParaCierreByCentroCosto($id_centro_costo, $conexion);

    // Get operational expenses ready for cash closing (all active expenses, not filtered by centro de costo)
    $gastos_operativos_para_cierre = GastoOperativo::getGastosOperativosParaCierre($conexion);

} catch (PDOException $e) {
    // Specific handling for database errors
    $error_display = 'show';
    $error_text    = "Error de base de datos al obtener los datos para cierre de caja.";
    // Initialize empty arrays to prevent undefined variable errors
    $citas_para_cierre = [];
    $ventas_para_cierre = [];
    $ordenes_compra_para_cierre = [];
    $gastos_operativos_para_cierre = [];
} catch (Exception $e) {
    // General error handling
    $error_display = 'show';
    $error_text    = $e->getMessage();
    // Initialize empty arrays to prevent undefined variable errors
    $citas_para_cierre = [];
    $ventas_para_cierre = [];
    $ordenes_compra_para_cierre = [];
    $gastos_operativos_para_cierre = [];
}
#endregion try

require_once __ROOT__ . '/views/cerrar_caja.view.php';
